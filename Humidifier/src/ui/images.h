#ifndef EEZ_LVGL_UI_IMAGES_H
#define EEZ_LVGL_UI_IMAGES_H

#include <lvgl/lvgl.h>

#ifdef __cplusplus
extern "C" {
#endif

extern const lv_img_dsc_t img_waterdrop;
extern const lv_img_dsc_t img_temp;
extern const lv_img_dsc_t img_fan;
extern const lv_img_dsc_t img_humidity;
extern const lv_img_dsc_t img_temp32;
extern const lv_img_dsc_t img_humidity32;
extern const lv_img_dsc_t img_fan32;
extern const lv_img_dsc_t img_drop32;

#ifndef EXT_IMG_DESC_T
#define EXT_IMG_DESC_T
typedef struct _ext_img_desc_t {
    const char *name;
    const lv_img_dsc_t *img_dsc;
} ext_img_desc_t;
#endif

extern const ext_img_desc_t images[8];


#ifdef __cplusplus
}
#endif

#endif /*EEZ_LVGL_UI_IMAGES_H*/