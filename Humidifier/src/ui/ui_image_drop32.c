#ifdef __has_include
    #if __has_include("lvgl.h")
        #ifndef LV_LVGL_H_INCLUDE_SIMPLE
            #define LV_LVGL_H_INCLUDE_SIMPLE
        #endif
    #endif
#endif

#if defined(LV_LVGL_H_INCLUDE_SIMPLE)
#include "lvgl.h"
#elif defined(LV_BUILD_TEST)
#include "../lvgl.h"
#else
#include "lvgl/lvgl.h"
#endif


#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif

#ifndef LV_ATTRIBUTE_IMG_DROP32
#define LV_ATTRIBUTE_IMG_DROP32
#endif

static const
LV_ATTRIBUTE_MEM_ALIGN LV_ATTRIBUTE_LARGE_CONST LV_ATTRIBUTE_IMG_DROP32
uint8_t img_drop32_map[] = {

    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xff,0xcc,0x80,0x14,0xff,0xcc,0x80,0x14,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xff,0xc7,0x83,0x40,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xff,0xc9,0x87,0x42,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xff,0xc9,0x85,0x47,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xff,0xc7,0x85,0x49,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfd,0xc2,0x77,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc8,0x85,0xff,0xfd,0xc5,0x7e,0xff,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xff,0xb9,0x74,0x0b,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xff,0xb3,0x66,0x0a,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xf5,0xa2,0x38,0xe3,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf8,0xa3,0x39,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xfe,0xb1,0x4f,0xff,0xf8,0xa3,0x3a,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xfe,0xb1,0x4f,0xff,0xff,0xb5,0x57,0xce,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xf6,0xa5,0x3c,0x77,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf6,0xa4,0x3d,0x76,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xea,0x92,0x21,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xea,0x92,0x21,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0xf4,0x9c,0x2e,0xff,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xf5,0xa3,0x3d,0x19,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xf6,0xa1,0x42,0x1b,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xec,0x97,0x2c,0xa2,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xec,0x97,0x2b,0xa5,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xe9,0x92,0x24,0xd9,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe5,0x8c,0x1b,0xff,0xe8,0x91,0x24,0xdd,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xe7,0x8f,0x20,0x80,0xe7,0x8f,0x20,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,

};

const lv_image_dsc_t img_drop32 = {
  .header.magic = LV_IMAGE_HEADER_MAGIC,
  .header.cf = LV_COLOR_FORMAT_ARGB8888,
  .header.flags = 0,
  .header.w = 32,
  .header.h = 32,
  .header.stride = 128,
  .data_size = sizeof(img_drop32_map),
  .data = img_drop32_map,
};

