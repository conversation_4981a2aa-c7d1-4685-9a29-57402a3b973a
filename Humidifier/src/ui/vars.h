#ifndef EEZ_LVGL_UI_VARS_H
#define EEZ_LVGL_UI_VARS_H

#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

// enum declarations



// Flow global variables

enum FlowGlobalVariables {
    FLOW_GLOBAL_VARIABLE_NONE
};

// Native global variables

extern int32_t get_var_humidity();
extern void set_var_humidity(int32_t value);
extern float get_var_temp();
extern void set_var_temp(float value);
extern int32_t get_var_speed();
extern void set_var_speed(int32_t value);
extern float get_var_water_level();
extern void set_var_water_level(float value);


#ifdef __cplusplus
}
#endif

#endif /*EEZ_LVGL_UI_VARS_H*/